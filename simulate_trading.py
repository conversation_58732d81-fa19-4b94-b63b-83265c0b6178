#!/usr/bin/env python3
"""
Trading Simulation - Generate realistic trading data to show what the dashboard would look like
"""

import json
import csv
import random
from datetime import datetime, timedelta
import os

def create_sample_portfolio():
    """Create sample portfolio data"""
    portfolio = {
        "total_value": 12450.75,
        "available_balance": 8230.50,
        "total_pnl": 2450.75,
        "positions": {
            "MATIC/USDC": {
                "quantity": 5000.0,
                "entry_price": 0.742,
                "current_price": 0.758,
                "unrealized_pnl": 80.0,
                "timestamp": datetime.now().isoformat()
            },
            "WETH/USDC": {
                "quantity": 1.25,
                "entry_price": 3200.0,
                "current_price": 3185.0,
                "unrealized_pnl": -18.75,
                "timestamp": datetime.now().isoformat()
            }
        },
        "last_updated": datetime.now().isoformat()
    }
    
    # Create data directory if it doesn't exist
    os.makedirs('data', exist_ok=True)
    
    with open('data/portfolio_state.json', 'w') as f:
        json.dump(portfolio, f, indent=2)
    
    print("✅ Created sample portfolio data")

def create_sample_trades():
    """Create sample trading history"""
    trades = []
    
    # Generate 15 sample trades over the last 24 hours
    base_time = datetime.now() - timedelta(hours=24)
    
    symbols = ['MATIC/USDC', 'WETH/USDC', 'USDT/USDC', 'WBTC/USDC', 'LINK/USDC']
    
    for i in range(15):
        trade_time = base_time + timedelta(hours=i*1.6)
        symbol = random.choice(symbols)
        side = random.choice(['BUY', 'SELL'])
        
        # Realistic prices
        prices = {
            'MATIC/USDC': random.uniform(0.72, 0.78),
            'WETH/USDC': random.uniform(3150, 3250),
            'USDT/USDC': random.uniform(0.998, 1.002),
            'WBTC/USDC': random.uniform(64000, 66000),
            'LINK/USDC': random.uniform(14, 15)
        }
        
        entry_price = prices[symbol]
        exit_price = entry_price * random.uniform(0.98, 1.03)  # ±3% variation
        quantity = random.uniform(100, 2000) if 'USDC' in symbol else random.uniform(0.1, 2.0)
        
        pnl = (exit_price - entry_price) * quantity if side == 'BUY' else (entry_price - exit_price) * quantity
        gas_fee = random.uniform(0.50, 2.50)  # Polygon gas fees
        
        status = random.choice(['COMPLETED', 'COMPLETED', 'COMPLETED', 'STOP_LOSS', 'TARGET_REACHED'])
        
        trade = {
            'timestamp': trade_time.strftime('%Y-%m-%d %H:%M:%S'),
            'symbol': symbol,
            'side': side,
            'entry_price': round(entry_price, 4),
            'exit_price': round(exit_price, 4),
            'quantity': round(quantity, 3),
            'pnl': round(pnl, 2),
            'gas_fee': round(gas_fee, 2),
            'status': status
        }
        trades.append(trade)
    
    # Write to CSV
    os.makedirs('logs', exist_ok=True)
    with open('logs/trades.csv', 'w', newline='') as f:
        if trades:
            writer = csv.DictWriter(f, fieldnames=trades[0].keys())
            writer.writeheader()
            writer.writerows(trades)
    
    print(f"✅ Created {len(trades)} sample trades")

def create_sample_signals():
    """Create sample signals"""
    signals = []
    
    base_time = datetime.now() - timedelta(hours=12)
    symbols = ['MATIC/USDC', 'WETH/USDC', 'USDT/USDC', 'WBTC/USDC', 'LINK/USDC']
    
    for i in range(8):
        signal_time = base_time + timedelta(hours=i*1.5)
        symbol = random.choice(symbols)
        action = random.choice(['BUY', 'SELL'])
        
        prices = {
            'MATIC/USDC': random.uniform(0.72, 0.78),
            'WETH/USDC': random.uniform(3150, 3250),
            'USDT/USDC': 1.0,
            'WBTC/USDC': random.uniform(64000, 66000),
            'LINK/USDC': random.uniform(14, 15)
        }
        
        signal = {
            "timestamp": signal_time.isoformat(),
            "asset": symbol,
            "action": action,
            "entry_price": round(prices[symbol], 4),
            "confidence": round(random.uniform(0.6, 0.95), 2),
            "predictions": {
                "1m": round(random.uniform(-2, 3), 1),
                "5m": round(random.uniform(-1, 2), 1),
                "15m": round(random.uniform(-0.5, 1.5), 1)
            },
            "volume_analysis": {
                "volume_ratio": round(random.uniform(0.8, 2.5), 2),
                "liquidity_score": round(random.uniform(0.7, 1.0), 2)
            },
            "sentiment_score": round(random.uniform(0.3, 0.8), 2),
            "signal_strength": round(random.uniform(0.6, 0.9), 2)
        }
        signals.append(signal)
    
    # Write signals as JSON array
    with open('logs/signals.json', 'w') as f:
        json.dump(signals, f, indent=2)
    
    print(f"✅ Created {len(signals)} sample signals")

def create_sample_performance():
    """Create sample performance metrics"""
    performance_data = []
    
    base_time = datetime.now() - timedelta(hours=2)
    
    for i in range(24):  # Last 2 hours, every 5 minutes
        perf_time = base_time + timedelta(minutes=i*5)
        
        perf = {
            "timestamp": perf_time.isoformat(),
            "data_quality_score": round(random.uniform(0.95, 1.0), 2),
            "assets_processed": random.randint(8, 10),
            "signals_generated": random.randint(0, 3),
            "cycle_time_ms": random.randint(800, 1500),
            "memory_usage_mb": round(random.uniform(180, 220), 1),
            "portfolio_value": round(12000 + random.uniform(-200, 300), 2),
            "total_pnl": round(2400 + random.uniform(-50, 100), 2)
        }
        performance_data.append(perf)
    
    # Write performance data (one JSON object per line)
    with open('logs/performance.json', 'w') as f:
        for perf in performance_data:
            f.write(json.dumps(perf) + '\n')
    
    print(f"✅ Created {len(performance_data)} performance data points")

def main():
    """Generate all sample data"""
    print("🎯 GENERATING SAMPLE TRADING DATA")
    print("=" * 40)
    
    create_sample_portfolio()
    create_sample_trades()
    create_sample_signals()
    create_sample_performance()
    
    print("\n✅ Sample data generated successfully!")
    print("\n💡 Now run: python3 trading_dashboard.py")
    print("💡 This will show you what the live trading metrics look like")

if __name__ == "__main__":
    main()
