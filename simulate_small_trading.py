#!/usr/bin/env python3
"""
Small Portfolio Trading Simulation - $50 starting capital
Realistic trades for small-scale DeFi trading on Polygon
"""

import json
import csv
import random
from datetime import datetime, timedelta
import os

def create_small_portfolio():
    """Create realistic $50 portfolio data"""
    portfolio = {
        "total_value": 52.35,  # Started with $50, now $52.35
        "available_balance": 31.20,  # Available for new trades
        "total_pnl": 2.35,  # $2.35 profit so far
        "positions": {
            "MATIC/USDC": {
                "quantity": 25.0,  # 25 MATIC tokens
                "entry_price": 0.742,
                "current_price": 0.758,
                "unrealized_pnl": 0.40,  # Small but positive
                "value_usd": 18.95,
                "timestamp": datetime.now().isoformat()
            },
            "USDC": {
                "quantity": 2.20,  # Small USDC position
                "entry_price": 1.0,
                "current_price": 1.0,
                "unrealized_pnl": 0.0,
                "value_usd": 2.20,
                "timestamp": datetime.now().isoformat()
            }
        },
        "starting_capital": 50.0,
        "last_updated": datetime.now().isoformat()
    }
    
    os.makedirs('data', exist_ok=True)
    
    with open('data/portfolio_state.json', 'w') as f:
        json.dump(portfolio, f, indent=2)
    
    print("✅ Created $50 portfolio data")

def create_small_trades():
    """Create realistic small trades for $50 portfolio"""
    trades = []
    
    # Generate 12 small trades over the last 48 hours
    base_time = datetime.now() - timedelta(hours=48)
    
    # Focus on cheaper tokens that make sense for $50 portfolio
    symbols = ['MATIC/USDC', 'USDC/USDT', 'LINK/USDC']
    
    for i in range(12):
        trade_time = base_time + timedelta(hours=i*4)
        symbol = random.choice(symbols)
        side = random.choice(['BUY', 'SELL'])
        
        # Realistic small trade sizes
        if symbol == 'MATIC/USDC':
            quantity = random.uniform(5, 50)  # 5-50 MATIC tokens
            entry_price = random.uniform(0.72, 0.78)
            exit_price = entry_price * random.uniform(0.985, 1.025)  # ±2.5% variation
        elif symbol == 'LINK/USDC':
            quantity = random.uniform(0.5, 3.0)  # 0.5-3 LINK tokens
            entry_price = random.uniform(14, 15)
            exit_price = entry_price * random.uniform(0.98, 1.03)
        else:  # USDC/USDT
            quantity = random.uniform(5, 25)  # $5-25 trades
            entry_price = random.uniform(0.998, 1.002)
            exit_price = entry_price * random.uniform(0.999, 1.001)
        
        # Calculate realistic P&L for small trades
        trade_value = quantity * entry_price
        if trade_value > 50:  # Don't allow trades larger than portfolio
            quantity = 45 / entry_price  # Max $45 trade
            trade_value = 45
        
        pnl = (exit_price - entry_price) * quantity if side == 'BUY' else (entry_price - exit_price) * quantity
        
        # Polygon gas fees are very low
        gas_fee = random.uniform(0.01, 0.05)  # $0.01-0.05 gas fees
        
        # Net P&L after gas
        net_pnl = pnl - gas_fee
        
        status = random.choice(['COMPLETED', 'COMPLETED', 'TARGET_REACHED', 'STOP_LOSS'])
        
        trade = {
            'timestamp': trade_time.strftime('%Y-%m-%d %H:%M:%S'),
            'symbol': symbol,
            'side': side,
            'entry_price': round(entry_price, 4),
            'exit_price': round(exit_price, 4),
            'quantity': round(quantity, 3),
            'trade_value': round(trade_value, 2),
            'pnl': round(pnl, 3),
            'gas_fee': round(gas_fee, 3),
            'net_pnl': round(net_pnl, 3),
            'status': status
        }
        trades.append(trade)
    
    # Write to CSV
    os.makedirs('logs', exist_ok=True)
    with open('logs/trades.csv', 'w', newline='') as f:
        if trades:
            writer = csv.DictWriter(f, fieldnames=trades[0].keys())
            writer.writeheader()
            writer.writerows(trades)
    
    print(f"✅ Created {len(trades)} realistic small trades")

def create_small_signals():
    """Create realistic signals for small portfolio"""
    signals = []
    
    base_time = datetime.now() - timedelta(hours=24)
    symbols = ['MATIC/USDC', 'USDC/USDT', 'LINK/USDC']
    
    for i in range(6):
        signal_time = base_time + timedelta(hours=i*4)
        symbol = random.choice(symbols)
        action = random.choice(['BUY', 'SELL'])
        
        # Realistic prices for small trades
        prices = {
            'MATIC/USDC': random.uniform(0.72, 0.78),
            'USDC/USDT': random.uniform(0.998, 1.002),
            'LINK/USDC': random.uniform(14, 15)
        }
        
        # Suggested trade sizes appropriate for $50 portfolio
        trade_sizes = {
            'MATIC/USDC': random.uniform(10, 30),  # $7-23 trades
            'USDC/USDT': random.uniform(5, 15),   # $5-15 trades
            'LINK/USDC': random.uniform(0.5, 2.0) # $7-30 trades
        }
        
        signal = {
            "timestamp": signal_time.isoformat(),
            "asset": symbol,
            "action": action,
            "entry_price": round(prices[symbol], 4),
            "suggested_quantity": round(trade_sizes[symbol], 3),
            "trade_value": round(trade_sizes[symbol] * prices[symbol], 2),
            "confidence": round(random.uniform(0.6, 0.9), 2),
            "risk_level": random.choice(["LOW", "MEDIUM"]),  # No HIGH risk for small portfolio
            "predictions": {
                "1m": round(random.uniform(-1, 2), 1),
                "5m": round(random.uniform(-0.5, 1.5), 1),
                "15m": round(random.uniform(-0.3, 1.0), 1)
            },
            "stop_loss": round(prices[symbol] * 0.97, 4),  # 3% stop loss
            "take_profit": round(prices[symbol] * 1.05, 4),  # 5% take profit
            "max_loss": round(trade_sizes[symbol] * prices[symbol] * 0.03, 2)  # Max $1.50 loss
        }
        signals.append(signal)
    
    # Write signals as JSON array
    with open('logs/signals.json', 'w') as f:
        json.dump(signals, f, indent=2)
    
    print(f"✅ Created {len(signals)} small portfolio signals")

def create_small_performance():
    """Create performance metrics for small portfolio"""
    performance_data = []
    
    base_time = datetime.now() - timedelta(hours=4)
    
    for i in range(48):  # Last 4 hours, every 5 minutes
        perf_time = base_time + timedelta(minutes=i*5)
        
        # Portfolio value fluctuating around $50-55
        portfolio_value = 50 + random.uniform(-2, 5)
        total_pnl = portfolio_value - 50
        
        perf = {
            "timestamp": perf_time.isoformat(),
            "data_quality_score": round(random.uniform(0.95, 1.0), 2),
            "assets_processed": random.randint(3, 5),  # Fewer assets for small portfolio
            "signals_generated": random.randint(0, 2),
            "cycle_time_ms": random.randint(400, 800),  # Faster cycles
            "memory_usage_mb": round(random.uniform(120, 160), 1),  # Less memory usage
            "portfolio_value": round(portfolio_value, 2),
            "total_pnl": round(total_pnl, 2),
            "available_balance": round(portfolio_value * random.uniform(0.4, 0.7), 2),
            "position_count": random.randint(1, 3)
        }
        performance_data.append(perf)
    
    # Write performance data
    with open('logs/performance.json', 'w') as f:
        for perf in performance_data:
            f.write(json.dumps(perf) + '\n')
    
    print(f"✅ Created {len(performance_data)} small portfolio performance metrics")

def update_config_for_small_portfolio():
    """Update configuration for small portfolio trading"""
    small_config = {
        "STARTING_CAPITAL": 50.0,
        "MIN_TRADE_SIZE": 5.0,  # Minimum $5 trades
        "MAX_TRADE_SIZE": 25.0,  # Maximum $25 trades (50% of portfolio)
        "MAX_POSITION_SIZE": 0.4,  # Max 40% in single position
        "STOP_LOSS_PERCENT": 3.0,  # 3% stop loss
        "TAKE_PROFIT_PERCENT": 5.0,  # 5% take profit
        "MAX_DAILY_LOSS": 5.0,  # Max $5 loss per day (10% of portfolio)
        "GAS_FEE_LIMIT": 0.10,  # Max $0.10 gas fee per trade
        "PREFERRED_TOKENS": ["MATIC", "USDC", "USDT"],  # Focus on cheaper tokens
        "RISK_LEVEL": "CONSERVATIVE",  # Conservative for small portfolio
        "REBALANCE_THRESHOLD": 2.0  # Rebalance when $2+ profit
    }
    
    os.makedirs('config', exist_ok=True)
    with open('config/small_portfolio.json', 'w') as f:
        json.dump(small_config, f, indent=2)
    
    print("✅ Created small portfolio configuration")

def main():
    """Generate all small portfolio data"""
    print("💰 GENERATING $50 PORTFOLIO TRADING DATA")
    print("=" * 50)
    
    create_small_portfolio()
    create_small_trades()
    create_small_signals()
    create_small_performance()
    update_config_for_small_portfolio()
    
    print("\n✅ $50 Portfolio data generated successfully!")
    print("\n📊 PORTFOLIO SUMMARY:")
    print("   💵 Starting Capital: $50.00")
    print("   📈 Current Value: $52.35")
    print("   💰 Total P&L: +$2.35 (+4.7%)")
    print("   🎯 Active Positions: 2")
    print("   ⛽ Total Gas Fees: <$0.50")
    print("\n💡 Now run: python3 trading_dashboard.py")
    print("💡 Perfect for testing with small amounts!")

if __name__ == "__main__":
    main()
